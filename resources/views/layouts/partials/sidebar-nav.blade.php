{{-- Sidebar Navigation for UAE English Sports Academy --}}
{{-- Bank-style navigation with role-based menu items --}}

<div class="nav-item">
    <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span class="nav-text">Dashboard</span>
    </a>
</div>

@if(Auth::user()->role === 'admin' || Auth::user()->role === 'branch_manager')
    {{-- Branch Management --}}
    <div class="nav-item">
        <a href="#" class="nav-link {{ request()->routeIs('branches.*') ? 'active' : '' }}">
            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            <span class="nav-text">Branch Management</span>
        </a>
    </div>
@endif

{{-- Academy Management --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('academies.*') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
        </svg>
        <span class="nav-text">Academy Management</span>
    </a>
</div>

{{-- Program Management --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('programs.*') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
        </svg>
        <span class="nav-text">Program Management</span>
    </a>
</div>

{{-- Student Management --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('students.*') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
        </svg>
        <span class="nav-text">Students</span>
    </a>
</div>

{{-- Payment Management --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('payments.*') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
        </svg>
        <span class="nav-text">Payment Management</span>
    </a>
</div>

{{-- Uniform Management --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('uniforms.*') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
        </svg>
        <span class="nav-text">Uniform Management</span>
    </a>
</div>

{{-- Reports Section --}}
<div class="nav-item mt-6">
    <div class="px-4 py-2">
        <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Reports</h4>
    </div>
</div>

{{-- Financial Reports --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('reports.financial') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span class="nav-text">Financial Reports</span>
    </a>
</div>

{{-- Uniform Reports --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('reports.uniform') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="nav-text">Uniform Reports</span>
    </a>
</div>

{{-- Program Reports --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('reports.program') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <span class="nav-text">Program Reports</span>
    </a>
</div>

{{-- Status Reports --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('reports.status') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="nav-text">Status Reports</span>
    </a>
</div>

{{-- Daily Reports --}}
<div class="nav-item">
    <a href="#" class="nav-link {{ request()->routeIs('reports.daily') ? 'active' : '' }}">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <span class="nav-text">Daily Reports</span>
    </a>
</div>

@if(Auth::user()->role === 'admin')
    {{-- Admin Section --}}
    <div class="nav-item mt-6">
        <div class="px-4 py-2">
            <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Administration</h4>
        </div>
    </div>

    {{-- User Management --}}
    <div class="nav-item">
        <a href="#" class="nav-link {{ request()->routeIs('users.*') ? 'active' : '' }}">
            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span class="nav-text">User Management</span>
        </a>
    </div>

    {{-- System Settings --}}
    <div class="nav-item">
        <a href="#" class="nav-link {{ request()->routeIs('settings.*') ? 'active' : '' }}">
            <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="nav-text">Settings</span>
        </a>
    </div>
@endif

{{-- Quick Actions Section --}}
<div class="nav-item mt-6">
    <div class="px-4 py-2">
        <h4 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Quick Actions</h4>
    </div>
</div>

{{-- Add Student --}}
<div class="nav-item">
    <a href="#" class="nav-link" onclick="openModal('create', 'student')">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="nav-text">Add Student</span>
    </a>
</div>

{{-- Add Payment --}}
<div class="nav-item">
    <a href="#" class="nav-link" onclick="openModal('create', 'payment')">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="nav-text">Add Payment</span>
    </a>
</div>

{{-- Order Uniform --}}
<div class="nav-item">
    <a href="#" class="nav-link" onclick="openModal('create', 'uniform')">
        <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <span class="nav-text">Order Uniform</span>
    </a>
</div>
