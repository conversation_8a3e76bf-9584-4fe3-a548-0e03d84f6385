<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Branch extends Model
{
    protected $fillable = [
        'name',
        'location',
        'phone',
        'email',
        'address',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Get the academies for the branch.
     */
    public function academies(): Has<PERSON>any
    {
        return $this->hasMany(Academy::class);
    }

    /**
     * Get the students for the branch.
     */
    public function students(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Student::class);
    }

    /**
     * Get the payments for the branch.
     */
    public function payments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the uniforms for the branch.
     */
    public function uniforms(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the users for the branch.
     */
    public function users(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }
}
